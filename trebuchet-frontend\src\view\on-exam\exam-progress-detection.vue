<template>
  <div>
    <!-- 考试选择 -->
    <Card style="margin-bottom: 20px">
      <Row>
        <Col span="8">
          <Form-item label="选择考试">
            <Select v-model="selectedExam" placeholder="请选择要监测的考试" @on-change="onExamChange">
              <Option v-for="exam in examList" :key="exam.id" :value="exam.id">
                {{ exam.id }} : {{ exam.date }} - {{ exam.name }}
              </Option>
            </Select>
          </Form-item>
        </Col>
        <Col span="4" offset="1">
          <Button type="primary" @click="startMonitoring" :disabled="!selectedExam">开始监测</Button>
          <Button type="warning" style="margin-left: 10px" @click="stopMonitoring" :disabled="!isMonitoring"
            >停止监测</Button
          >
        </Col>
      </Row>
    </Card>

    <!-- 考试通过人数检测及无人通过警报 -->
    <Card>
      <p slot="title">
        <Icon type="ios-analytics" />
        考试通过人数实时监测
        <Tag v-if="isMonitoring" color="success" style="margin-left: 10px">监测中</Tag>
        <Tag v-else color="default" style="margin-left: 10px">未监测</Tag>
      </p>
      <!-- 警报区域 -->
      <div v-if="alertInfo.show" style="margin-bottom: 20px">
        <Alert :type="alertInfo.type" :banner="true" show-icon closable @on-close="dismissAlert">
          <template slot="desc">
            {{ alertInfo.message }}
          </template>
        </Alert>
      </div>

      <!-- 统计信息 -->
      <Row style="margin-bottom: 20px">
        <Col span="6">
          <Statistic title="考试总人数" :value="examStats.totalStudents" />
        </Col>
        <Col span="6">
          <Statistic title="已通过人数" :value="examStats.passedStudents" />
        </Col>
        <Col span="6">
          <Statistic title="通过率" :value="examStats.passRate" suffix="%" />
        </Col>
        <Col span="6">
          <Statistic title="考试持续时间" :value="examStats.duration" suffix="分钟" />
        </Col>
      </Row>

      <!-- 通过学生列表 -->
      <Tabs value="passed">
        <TabPane label="已通过学生" name="passed">
          <Table :data="passedStudents" :columns="passedColumns" :loading="loading" size="small" />
        </TabPane>
        <TabPane label="未通过学生" name="failed">
          <Table :data="failedStudents" :columns="failedColumns" :loading="loading" size="small" />
        </TabPane>
      </Tabs>

      <!-- 刷新按钮 -->
      <Row style="margin-top: 20px">
        <Col>
          <Button type="primary" @click="refreshData" :loading="loading">
            <Icon type="ios-refresh" />
            刷新数据
          </Button>
          <span style="margin-left: 10px; color: #999"> 最后更新时间: {{ lastUpdateTime }} </span>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
// import { userProfileReq } from '@/api/user'
// import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'ExamProgressDetection',
  data() {
    return {
      selectedExam: null,
      isMonitoring: false,
      loading: false,
      lastUpdateTime: '',
      monitoringTimer: null,
      // 模拟考试列表
      examList: [
        { id: 1, date: '2024-01-25', name: 'P1课上考试' },
        { id: 2, date: '2024-01-26', name: 'P2课上考试' },
        { id: 3, date: '2024-01-27', name: 'P3课上考试' }
      ],
      // 考试统计信息
      examStats: {
        totalStudents: 45,
        passedStudents: 12,
        passRate: 26.7,
        duration: 85
      },
      // 警报信息
      alertInfo: {
        show: false,
        type: 'warning',
        message: ''
      },
      // 已通过学生列表
      passedStudents: [
        {
          id: 1,
          student_name: '张三',
          student_id: '20210001',
          department: '计算机学院',
          pass_time: '2024-01-25 14:30:25',
          score: 95,
          duration: '45分钟'
        },
        {
          id: 2,
          student_name: '李四',
          student_id: '20210002',
          department: '软件学院',
          pass_time: '2024-01-25 14:35:12',
          score: 88,
          duration: '50分钟'
        },
        {
          id: 3,
          student_name: '王五',
          student_id: '20210003',
          department: '计算机学院',
          pass_time: '2024-01-25 14:42:08',
          score: 92,
          duration: '57分钟'
        }
      ],
      // 未通过学生列表
      failedStudents: [
        {
          id: 1,
          student_name: '赵六',
          student_id: '20210004',
          department: '软件学院',
          current_score: 65,
          attempts: 3,
          last_attempt: '2024-01-25 15:10:30'
        },
        {
          id: 2,
          student_name: '孙七',
          student_id: '20210005',
          department: '计算机学院',
          current_score: 58,
          attempts: 2,
          last_attempt: '2024-01-25 15:05:15'
        }
      ],
      // 表格列定义
      passedColumns: [
        { title: '姓名', key: 'student_name', width: 100 },
        { title: '学号', key: 'student_id', width: 120 },
        { title: '学院', key: 'department', width: 120 },
        { title: '通过时间', key: 'pass_time', width: 150 },
        { title: '分数', key: 'score', width: 80 },
        { title: '用时', key: 'duration', width: 100 }
      ],
      failedColumns: [
        { title: '姓名', key: 'student_name', width: 100 },
        { title: '学号', key: 'student_id', width: 120 },
        { title: '学院', key: 'department', width: 120 },
        { title: '当前分数', key: 'current_score', width: 100 },
        { title: '尝试次数', key: 'attempts', width: 100 },
        { title: '最后尝试时间', key: 'last_attempt', width: 150 }
      ]
    }
  },
  mounted() {
    this.updateLastUpdateTime()
  },
  beforeDestroy() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer)
    }
  },
  methods: {
    onExamChange(examId) {
      this.selectedExam = examId
      this.refreshData()
    },
    startMonitoring() {
      if (!this.selectedExam) {
        this.$Modal.warning({ title: '请先选择要监测的考试' })
        return
      }

      this.isMonitoring = true
      this.$Notice.success({ title: '开始监测', desc: '已开始实时监测考试通过情况' })

      // 设置定时器，每30秒刷新一次数据
      this.monitoringTimer = setInterval(() => {
        this.refreshData()
        this.checkForAlerts()
      }, 30000)

      this.refreshData()
    },
    stopMonitoring() {
      this.isMonitoring = false
      if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer)
        this.monitoringTimer = null
      }
      this.$Notice.info({ title: '停止监测', desc: '已停止实时监测' })
    },
    refreshData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        // 模拟数据更新
        this.examStats.passedStudents = Math.min(
          this.examStats.totalStudents,
          this.examStats.passedStudents + Math.floor(Math.random() * 3)
        )
        this.examStats.passRate = ((this.examStats.passedStudents / this.examStats.totalStudents) * 100).toFixed(1)
        this.examStats.duration += 1

        this.updateLastUpdateTime()
        this.loading = false
      }, 800)
    },
    checkForAlerts() {
      // const currentTime = new Date()
      // const examStartTime = new Date(currentTime.getTime() - this.examStats.duration * 60000)

      // 检查是否考试进行超过60分钟且无人通过
      if (this.examStats.duration > 60 && this.examStats.passedStudents === 0) {
        this.showAlert('error', '警告：考试已进行超过60分钟，但仍无人通过！请检查考试难度或学生状态。')
      }
      // 检查通过率是否过低
      else if (this.examStats.duration > 30 && this.examStats.passRate < 10) {
        this.showAlert(
          'warning',
          `注意：考试进行${this.examStats.duration}分钟，通过率仅为${this.examStats.passRate}%，建议关注考试情况。`
        )
      }
    },
    showAlert(type, message) {
      this.alertInfo = {
        show: true,
        type: type,
        message: message
      }
    },
    dismissAlert() {
      this.alertInfo.show = false
    },
    updateLastUpdateTime() {
      const now = new Date()
      this.lastUpdateTime = now.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.ivu-card {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}

.ivu-card-head p {
  font-size: 16px;
  font-weight: 500;
}

.ivu-form-item {
  margin-bottom: 0;
}

.ivu-statistic {
  text-align: center;
}

.ivu-alert {
  animation: alertFadeIn 0.3s ease-in;
}

@keyframes alertFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
