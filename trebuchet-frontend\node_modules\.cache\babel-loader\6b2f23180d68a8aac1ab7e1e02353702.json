{"ast": null, "code": "// import { userProfileReq } from '@/api/user'\n// import { getErrModalOptions } from '@/libs/util'\n\nexport default {\n  name: 'ExamProgressDetection',\n  data() {\n    return {\n      selectedExam: null,\n      isMonitoring: false,\n      loading: false,\n      lastUpdateTime: '',\n      monitoringTimer: null,\n      // 模拟考试列表\n      examList: [{\n        id: 1,\n        date: '2024-01-25',\n        name: 'P1课上考试'\n      }, {\n        id: 2,\n        date: '2024-01-26',\n        name: 'P2课上考试'\n      }, {\n        id: 3,\n        date: '2024-01-27',\n        name: 'P3课上考试'\n      }],\n      // 考试统计信息\n      examStats: {\n        totalStudents: 45,\n        passedStudents: 12,\n        passRate: 26.7,\n        duration: 85\n      },\n      // 警报信息\n      alertInfo: {\n        show: false,\n        type: 'warning',\n        message: ''\n      },\n      // 已通过学生列表\n      passedStudents: [{\n        id: 1,\n        student_name: '张三',\n        student_id: '20210001',\n        department: '计算机学院',\n        pass_time: '2024-01-25 14:30:25',\n        score: 95,\n        duration: '45分钟'\n      }, {\n        id: 2,\n        student_name: '李四',\n        student_id: '20210002',\n        department: '软件学院',\n        pass_time: '2024-01-25 14:35:12',\n        score: 88,\n        duration: '50分钟'\n      }, {\n        id: 3,\n        student_name: '王五',\n        student_id: '20210003',\n        department: '计算机学院',\n        pass_time: '2024-01-25 14:42:08',\n        score: 92,\n        duration: '57分钟'\n      }],\n      // 未通过学生列表\n      failedStudents: [{\n        id: 1,\n        student_name: '赵六',\n        student_id: '20210004',\n        department: '软件学院',\n        current_score: 65,\n        attempts: 3,\n        last_attempt: '2024-01-25 15:10:30'\n      }, {\n        id: 2,\n        student_name: '孙七',\n        student_id: '20210005',\n        department: '计算机学院',\n        current_score: 58,\n        attempts: 2,\n        last_attempt: '2024-01-25 15:05:15'\n      }],\n      // 表格列定义\n      passedColumns: [{\n        title: '姓名',\n        key: 'student_name',\n        width: 100\n      }, {\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '学院',\n        key: 'department',\n        width: 120\n      }, {\n        title: '通过时间',\n        key: 'pass_time',\n        width: 150\n      }, {\n        title: '分数',\n        key: 'score',\n        width: 80\n      }, {\n        title: '用时',\n        key: 'duration',\n        width: 100\n      }],\n      failedColumns: [{\n        title: '姓名',\n        key: 'student_name',\n        width: 100\n      }, {\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '学院',\n        key: 'department',\n        width: 120\n      }, {\n        title: '当前分数',\n        key: 'current_score',\n        width: 100\n      }, {\n        title: '尝试次数',\n        key: 'attempts',\n        width: 100\n      }, {\n        title: '最后尝试时间',\n        key: 'last_attempt',\n        width: 150\n      }]\n    };\n  },\n  mounted() {\n    this.updateLastUpdateTime();\n  },\n  beforeDestroy() {\n    if (this.monitoringTimer) {\n      clearInterval(this.monitoringTimer);\n    }\n  },\n  methods: {\n    onExamChange(examId) {\n      this.selectedExam = examId;\n      this.refreshData();\n    },\n    startMonitoring() {\n      if (!this.selectedExam) {\n        this.$Modal.warning({\n          title: '请先选择要监测的考试'\n        });\n        return;\n      }\n      this.isMonitoring = true;\n      this.$Notice.success({\n        title: '开始监测',\n        desc: '已开始实时监测考试通过情况'\n      });\n\n      // 设置定时器，每30秒刷新一次数据\n      this.monitoringTimer = setInterval(() => {\n        this.refreshData();\n        this.checkForAlerts();\n      }, 30000);\n      this.refreshData();\n    },\n    stopMonitoring() {\n      this.isMonitoring = false;\n      if (this.monitoringTimer) {\n        clearInterval(this.monitoringTimer);\n        this.monitoringTimer = null;\n      }\n      this.$Notice.info({\n        title: '停止监测',\n        desc: '已停止实时监测'\n      });\n    },\n    refreshData() {\n      this.loading = true;\n      // 模拟API调用\n      setTimeout(() => {\n        // 模拟数据更新\n        this.examStats.passedStudents = Math.min(this.examStats.totalStudents, this.examStats.passedStudents + Math.floor(Math.random() * 3));\n        this.examStats.passRate = (this.examStats.passedStudents / this.examStats.totalStudents * 100).toFixed(1);\n        this.examStats.duration += 1;\n        this.updateLastUpdateTime();\n        this.loading = false;\n      }, 800);\n    },\n    checkForAlerts() {\n      const currentTime = new Date();\n      const examStartTime = new Date(currentTime.getTime() - this.examStats.duration * 60000);\n\n      // 检查是否考试进行超过60分钟且无人通过\n      if (this.examStats.duration > 60 && this.examStats.passedStudents === 0) {\n        this.showAlert('error', '警告：考试已进行超过60分钟，但仍无人通过！请检查考试难度或学生状态。');\n      }\n      // 检查通过率是否过低\n      else if (this.examStats.duration > 30 && this.examStats.passRate < 10) {\n        this.showAlert('warning', `注意：考试进行${this.examStats.duration}分钟，通过率仅为${this.examStats.passRate}%，建议关注考试情况。`);\n      }\n    },\n    showAlert(type, message) {\n      this.alertInfo = {\n        show: true,\n        type: type,\n        message: message\n      };\n    },\n    dismissAlert() {\n      this.alertInfo.show = false;\n    },\n    updateLastUpdateTime() {\n      const now = new Date();\n      this.lastUpdateTime = now.toLocaleString('zh-CN');\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAiFA;AACA;;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC,WACA;QAAAC;QAAAC;QAAAT;MAAA,GACA;QAAAQ;QAAAC;QAAAT;MAAA,GACA;QAAAQ;QAAAC;QAAAT;MAAA,EACA;MACA;MACAU;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAN,iBACA;QACAJ;QACAW;QACAC;QACAC;QACAC;QACAC;QACAT;MACA,GACA;QACAN;QACAW;QACAC;QACAC;QACAC;QACAC;QACAT;MACA,GACA;QACAN;QACAW;QACAC;QACAC;QACAC;QACAC;QACAT;MACA,EACA;MACA;MACAU,iBACA;QACAhB;QACAW;QACAC;QACAC;QACAI;QACAC;QACAC;MACA,GACA;QACAnB;QACAW;QACAC;QACAC;QACAI;QACAC;QACAC;MACA,EACA;MACA;MACAC,gBACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC,gBACA;QAAAH;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA;IAEA;EACA;EACAE;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;UAAAT;QAAA;QACA;MACA;MAEA;MACA;QAAAA;QAAAU;MAAA;;MAEA;MACA;QACA;QACA;MACA;MAEA;IACA;IACAC;MACA;MACA;QACAL;QACA;MACA;MACA;QAAAN;QAAAU;MAAA;IACA;IACAE;MACA;MACA;MACAC;QACA;QACA;QACA;QACA;QAEA;QACA;MACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;MACA;MAAA,KACA;QACA;MACA;IACA;IACAC;MACA;QACA5B;QACAC;QACAC;MACA;IACA;IACA2B;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "selectedExam", "isMonitoring", "loading", "lastUpdateTime", "monitoringTimer", "examList", "id", "date", "examStats", "totalStudents", "passedStudents", "passRate", "duration", "alertInfo", "show", "type", "message", "student_name", "student_id", "department", "pass_time", "score", "failedStudents", "current_score", "attempts", "last_attempt", "passedColumns", "title", "key", "width", "failedColumns", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "onExamChange", "startMonitoring", "desc", "stopMonitoring", "refreshData", "setTimeout", "checkFor<PERSON>lerts", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "updateLastUpdateTime"], "sourceRoot": "src/view/on-exam", "sources": ["exam-progress-detection.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 考试选择 -->\n    <Card style=\"margin-bottom: 20px\">\n      <Row>\n        <Col span=\"8\">\n          <Form-item label=\"选择考试\">\n            <Select v-model=\"selectedExam\" placeholder=\"请选择要监测的考试\" @on-change=\"onExamChange\">\n              <Option v-for=\"exam in examList\" :key=\"exam.id\" :value=\"exam.id\">\n                {{ exam.id }} : {{ exam.date }} - {{ exam.name }}\n              </Option>\n            </Select>\n          </Form-item>\n        </Col>\n        <Col span=\"4\" offset=\"1\">\n          <Button type=\"primary\" @click=\"startMonitoring\" :disabled=\"!selectedExam\">开始监测</Button>\n          <Button type=\"warning\" style=\"margin-left: 10px\" @click=\"stopMonitoring\" :disabled=\"!isMonitoring\"\n            >停止监测</Button\n          >\n        </Col>\n      </Row>\n    </Card>\n\n    <!-- 考试通过人数检测及无人通过警报 -->\n    <Card>\n      <p slot=\"title\">\n        <Icon type=\"ios-analytics\" />\n        考试通过人数实时监测\n        <Tag v-if=\"isMonitoring\" color=\"success\" style=\"margin-left: 10px\">监测中</Tag>\n        <Tag v-else color=\"default\" style=\"margin-left: 10px\">未监测</Tag>\n      </p>\n      <!-- 警报区域 -->\n      <div v-if=\"alertInfo.show\" style=\"margin-bottom: 20px\">\n        <Alert :type=\"alertInfo.type\" :banner=\"true\" show-icon closable @on-close=\"dismissAlert\">\n          <template slot=\"desc\">\n            {{ alertInfo.message }}\n          </template>\n        </Alert>\n      </div>\n\n      <!-- 统计信息 -->\n      <Row style=\"margin-bottom: 20px\">\n        <Col span=\"6\">\n          <Statistic title=\"考试总人数\" :value=\"examStats.totalStudents\" />\n        </Col>\n        <Col span=\"6\">\n          <Statistic title=\"已通过人数\" :value=\"examStats.passedStudents\" />\n        </Col>\n        <Col span=\"6\">\n          <Statistic title=\"通过率\" :value=\"examStats.passRate\" suffix=\"%\" />\n        </Col>\n        <Col span=\"6\">\n          <Statistic title=\"考试持续时间\" :value=\"examStats.duration\" suffix=\"分钟\" />\n        </Col>\n      </Row>\n\n      <!-- 通过学生列表 -->\n      <Tabs value=\"passed\">\n        <TabPane label=\"已通过学生\" name=\"passed\">\n          <Table :data=\"passedStudents\" :columns=\"passedColumns\" :loading=\"loading\" size=\"small\" />\n        </TabPane>\n        <TabPane label=\"未通过学生\" name=\"failed\">\n          <Table :data=\"failedStudents\" :columns=\"failedColumns\" :loading=\"loading\" size=\"small\" />\n        </TabPane>\n      </Tabs>\n\n      <!-- 刷新按钮 -->\n      <Row style=\"margin-top: 20px\">\n        <Col>\n          <Button type=\"primary\" @click=\"refreshData\" :loading=\"loading\">\n            <Icon type=\"ios-refresh\" />\n            刷新数据\n          </Button>\n          <span style=\"margin-left: 10px; color: #999\"> 最后更新时间: {{ lastUpdateTime }} </span>\n        </Col>\n      </Row>\n    </Card>\n  </div>\n</template>\n\n<script>\n// import { userProfileReq } from '@/api/user'\n// import { getErrModalOptions } from '@/libs/util'\n\nexport default {\n  name: 'ExamProgressDetection',\n  data() {\n    return {\n      selectedExam: null,\n      isMonitoring: false,\n      loading: false,\n      lastUpdateTime: '',\n      monitoringTimer: null,\n      // 模拟考试列表\n      examList: [\n        { id: 1, date: '2024-01-25', name: 'P1课上考试' },\n        { id: 2, date: '2024-01-26', name: 'P2课上考试' },\n        { id: 3, date: '2024-01-27', name: 'P3课上考试' }\n      ],\n      // 考试统计信息\n      examStats: {\n        totalStudents: 45,\n        passedStudents: 12,\n        passRate: 26.7,\n        duration: 85\n      },\n      // 警报信息\n      alertInfo: {\n        show: false,\n        type: 'warning',\n        message: ''\n      },\n      // 已通过学生列表\n      passedStudents: [\n        {\n          id: 1,\n          student_name: '张三',\n          student_id: '20210001',\n          department: '计算机学院',\n          pass_time: '2024-01-25 14:30:25',\n          score: 95,\n          duration: '45分钟'\n        },\n        {\n          id: 2,\n          student_name: '李四',\n          student_id: '20210002',\n          department: '软件学院',\n          pass_time: '2024-01-25 14:35:12',\n          score: 88,\n          duration: '50分钟'\n        },\n        {\n          id: 3,\n          student_name: '王五',\n          student_id: '20210003',\n          department: '计算机学院',\n          pass_time: '2024-01-25 14:42:08',\n          score: 92,\n          duration: '57分钟'\n        }\n      ],\n      // 未通过学生列表\n      failedStudents: [\n        {\n          id: 1,\n          student_name: '赵六',\n          student_id: '20210004',\n          department: '软件学院',\n          current_score: 65,\n          attempts: 3,\n          last_attempt: '2024-01-25 15:10:30'\n        },\n        {\n          id: 2,\n          student_name: '孙七',\n          student_id: '20210005',\n          department: '计算机学院',\n          current_score: 58,\n          attempts: 2,\n          last_attempt: '2024-01-25 15:05:15'\n        }\n      ],\n      // 表格列定义\n      passedColumns: [\n        { title: '姓名', key: 'student_name', width: 100 },\n        { title: '学号', key: 'student_id', width: 120 },\n        { title: '学院', key: 'department', width: 120 },\n        { title: '通过时间', key: 'pass_time', width: 150 },\n        { title: '分数', key: 'score', width: 80 },\n        { title: '用时', key: 'duration', width: 100 }\n      ],\n      failedColumns: [\n        { title: '姓名', key: 'student_name', width: 100 },\n        { title: '学号', key: 'student_id', width: 120 },\n        { title: '学院', key: 'department', width: 120 },\n        { title: '当前分数', key: 'current_score', width: 100 },\n        { title: '尝试次数', key: 'attempts', width: 100 },\n        { title: '最后尝试时间', key: 'last_attempt', width: 150 }\n      ]\n    }\n  },\n  mounted() {\n    this.updateLastUpdateTime()\n  },\n  beforeDestroy() {\n    if (this.monitoringTimer) {\n      clearInterval(this.monitoringTimer)\n    }\n  },\n  methods: {\n    onExamChange(examId) {\n      this.selectedExam = examId\n      this.refreshData()\n    },\n    startMonitoring() {\n      if (!this.selectedExam) {\n        this.$Modal.warning({ title: '请先选择要监测的考试' })\n        return\n      }\n\n      this.isMonitoring = true\n      this.$Notice.success({ title: '开始监测', desc: '已开始实时监测考试通过情况' })\n      \n      // 设置定时器，每30秒刷新一次数据\n      this.monitoringTimer = setInterval(() => {\n        this.refreshData()\n        this.checkForAlerts()\n      }, 30000)\n      \n      this.refreshData()\n    },\n    stopMonitoring() {\n      this.isMonitoring = false\n      if (this.monitoringTimer) {\n        clearInterval(this.monitoringTimer)\n        this.monitoringTimer = null\n      }\n      this.$Notice.info({ title: '停止监测', desc: '已停止实时监测' })\n    },\n    refreshData() {\n      this.loading = true\n      // 模拟API调用\n      setTimeout(() => {\n        // 模拟数据更新\n        this.examStats.passedStudents = Math.min(this.examStats.totalStudents, this.examStats.passedStudents + Math.floor(Math.random() * 3))\n        this.examStats.passRate = ((this.examStats.passedStudents / this.examStats.totalStudents) * 100).toFixed(1)\n        this.examStats.duration += 1\n        \n        this.updateLastUpdateTime()\n        this.loading = false\n      }, 800)\n    },\n    checkForAlerts() {\n      const currentTime = new Date()\n      const examStartTime = new Date(currentTime.getTime() - this.examStats.duration * 60000)\n      \n      // 检查是否考试进行超过60分钟且无人通过\n      if (this.examStats.duration > 60 && this.examStats.passedStudents === 0) {\n        this.showAlert('error', '警告：考试已进行超过60分钟，但仍无人通过！请检查考试难度或学生状态。')\n      }\n      // 检查通过率是否过低\n      else if (this.examStats.duration > 30 && this.examStats.passRate < 10) {\n        this.showAlert('warning', `注意：考试进行${this.examStats.duration}分钟，通过率仅为${this.examStats.passRate}%，建议关注考试情况。`)\n      }\n    },\n    showAlert(type, message) {\n      this.alertInfo = {\n        show: true,\n        type: type,\n        message: message\n      }\n    },\n    dismissAlert() {\n      this.alertInfo.show = false\n    },\n    updateLastUpdateTime() {\n      const now = new Date()\n      this.lastUpdateTime = now.toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ivu-card {\n  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);\n}\n\n.ivu-card-head p {\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.ivu-form-item {\n  margin-bottom: 0;\n}\n\n.ivu-statistic {\n  text-align: center;\n}\n\n.ivu-alert {\n  animation: alertFadeIn 0.3s ease-in;\n}\n\n@keyframes alertFadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}