{"ast": null, "code": "import { userProfileReq } from '@/api/user';\nimport { getErrModalOptions } from '@/libs/util';\nexport default {\n  name: 'ProgressDetection',\n  data() {\n    return {\n      curCourse: null,\n      // 进度慢检测\n      slowDetection: {\n        submissionThreshold: 5,\n        daysRange: 7,\n        loading: false,\n        tableData: [{\n          id: 1,\n          student_name: '张三',\n          student_id: '20210001',\n          department: '计算机学院',\n          submission_count: 2,\n          current_project: 'P1',\n          last_submission_date: '2024-01-15'\n        }, {\n          id: 2,\n          student_name: '李四',\n          student_id: '20210002',\n          department: '软件学院',\n          submission_count: 1,\n          current_project: 'P2',\n          last_submission_date: '2024-01-10'\n        }],\n        columns: [{\n          title: 'ID',\n          key: 'id',\n          width: 80\n        }, {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        }, {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        }, {\n          title: '学院',\n          key: 'department',\n          width: 150\n        }, {\n          title: '提交次数',\n          key: 'submission_count',\n          width: 100\n        }, {\n          title: '当前项目',\n          key: 'current_project',\n          width: 120\n        }, {\n          title: '最后提交日期',\n          key: 'last_submission_date',\n          width: 150\n        }]\n      },\n      // 多次挂在同一个P检测\n      repeatedFailures: {\n        failureThreshold: 3,\n        loading: false,\n        tableData: [{\n          id: 1,\n          student_name: '王五',\n          student_id: '20210003',\n          department: '计算机学院',\n          current_project: 'P3',\n          failure_count: 4,\n          last_failure_date: '2024-01-20'\n        }, {\n          id: 2,\n          student_name: '赵六',\n          student_id: '20210004',\n          department: '软件学院',\n          current_project: 'P1',\n          failure_count: 5,\n          last_failure_date: '2024-01-18'\n        }],\n        columns: [{\n          title: 'ID',\n          key: 'id',\n          width: 80\n        }, {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        }, {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        }, {\n          title: '学院',\n          key: 'department',\n          width: 150\n        }, {\n          title: '当前项目',\n          key: 'current_project',\n          width: 120\n        }, {\n          title: '失败次数',\n          key: 'failure_count',\n          width: 100\n        }, {\n          title: '最后失败日期',\n          key: 'last_failure_date',\n          width: 150\n        }]\n      },\n      // 多次没有课上资格检测\n      qualificationFailures: {\n        failureThreshold: 2,\n        loading: false,\n        tableData: [{\n          id: 1,\n          student_name: '孙七',\n          student_id: '20210005',\n          department: '计算机学院',\n          current_project: 'P2',\n          missed_exams: 3,\n          current_status: '课下未通过',\n          last_exam_date: '2024-01-22'\n        }, {\n          id: 2,\n          student_name: '周八',\n          student_id: '20210006',\n          department: '软件学院',\n          current_project: 'P1',\n          missed_exams: 2,\n          current_status: '课下未通过',\n          last_exam_date: '2024-01-19'\n        }],\n        columns: [{\n          title: 'ID',\n          key: 'id',\n          width: 80\n        }, {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        }, {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        }, {\n          title: '学院',\n          key: 'department',\n          width: 150\n        }, {\n          title: '当前项目',\n          key: 'current_project',\n          width: 120\n        }, {\n          title: '错过考试次数',\n          key: 'missed_exams',\n          width: 120\n        }, {\n          title: '当前状态',\n          key: 'current_status',\n          width: 120\n        }, {\n          title: '最后考试日期',\n          key: 'last_exam_date',\n          width: 150\n        }]\n      }\n    };\n  },\n  mounted() {\n    this.loadCurCourse();\n  },\n  methods: {\n    loadCurCourse() {\n      userProfileReq('get').then(res => {\n        if (res.data.course === null) {\n          this.$Modal.info({\n            title: '请在课程信息/课程总览页面选择当前课程'\n          });\n        } else {\n          this.curCourse = res.data.course.id;\n        }\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    // 进度慢检测相关方法\n    detectSlowProgress() {\n      if (!this.curCourse) {\n        this.$Modal.warning({\n          title: '请先选择当前课程'\n        });\n        return;\n      }\n      this.slowDetection.loading = true;\n      // 模拟API调用\n      setTimeout(() => {\n        this.slowDetection.loading = false;\n        this.$Notice.success({\n          title: '检测完成',\n          desc: `发现 ${this.slowDetection.tableData.length} 名进度较慢的学生`\n        });\n      }, 1000);\n    },\n    exportSlowProgress() {\n      this.$Notice.success({\n        title: '导出成功',\n        desc: '进度慢检测结果已导出到CSV文件'\n      });\n    },\n    // 多次挂在同一个P检测相关方法\n    detectRepeatedFailures() {\n      if (!this.curCourse) {\n        this.$Modal.warning({\n          title: '请先选择当前课程'\n        });\n        return;\n      }\n      this.repeatedFailures.loading = true;\n      setTimeout(() => {\n        this.repeatedFailures.loading = false;\n        this.$Notice.success({\n          title: '检测完成',\n          desc: `发现 ${this.repeatedFailures.tableData.length} 名多次失败的学生`\n        });\n      }, 1000);\n    },\n    exportRepeatedFailures() {\n      this.$Notice.success({\n        title: '导出成功',\n        desc: '多次失败检测结果已导出到CSV文件'\n      });\n    },\n    // 多次没有课上资格检测相关方法\n    detectQualificationFailures() {\n      if (!this.curCourse) {\n        this.$Modal.warning({\n          title: '请先选择当前课程'\n        });\n        return;\n      }\n      this.qualificationFailures.loading = true;\n      setTimeout(() => {\n        this.qualificationFailures.loading = false;\n        this.$Notice.success({\n          title: '检测完成',\n          desc: `发现 ${this.qualificationFailures.tableData.length} 名多次失去资格的学生`\n        });\n      }, 1000);\n    },\n    exportQualificationFailures() {\n      this.$Notice.success({\n        title: '导出成功',\n        desc: '资格失败检测结果已导出到CSV文件'\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAsEA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC,YACA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAN;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA,EACA;QACAC,UACA;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA;MAEA;MACA;MACAC;QACAC;QACAd;QACAC,YACA;UACAC;UACAC;UACAC;UACAC;UACAE;UACAQ;UACAC;QACA,GACA;UACAd;UACAC;UACAC;UACAC;UACAE;UACAQ;UACAC;QACA,EACA;QACAP,UACA;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA;MAEA;MACA;MACAK;QACAH;QACAd;QACAC,YACA;UACAC;UACAC;UACAC;UACAC;UACAE;UACAW;UACAC;UACAC;QACA,GACA;UACAlB;UACAC;UACAC;UACAC;UACAE;UACAW;UACAC;UACAC;QACA,EACA;QACAX,UACA;UAAAC;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA;MAEA;IACA;EACA;EACAS;IACA;EACA;EACAC;IACAC;MACAC,sBACAC;QACA;UACA;YACAf;UACA;QACA;UACA;QACA;MACA,GACAgB;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;UAAAjB;QAAA;QACA;MACA;MACA;MACA;MACAkB;QACA;QACA;UACAlB;UACAmB;QACA;MACA;IACA;IACAC;MACA;QAAApB;QAAAmB;MAAA;IACA;IACA;IACAE;MACA;QACA;UAAArB;QAAA;QACA;MACA;MACA;MACAkB;QACA;QACA;UACAlB;UACAmB;QACA;MACA;IACA;IACAG;MACA;QAAAtB;QAAAmB;MAAA;IACA;IACA;IACAI;MACA;QACA;UAAAvB;QAAA;QACA;MACA;MACA;MACAkB;QACA;QACA;UACAlB;UACAmB;QACA;MACA;IACA;IACAK;MACA;QAAAxB;QAAAmB;MAAA;IACA;EACA;AACA", "names": ["name", "data", "curCourse", "slowDetection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loading", "tableData", "id", "student_name", "student_id", "department", "submission_count", "current_project", "last_submission_date", "columns", "title", "key", "width", "repeatedFailures", "failureT<PERSON><PERSON>old", "failure_count", "last_failure_date", "qualificationFailures", "missed_exams", "current_status", "last_exam_date", "mounted", "methods", "loadCurCourse", "userProfileReq", "then", "catch", "detectSlowProgress", "setTimeout", "desc", "exportSlowProgress", "detectRepeatedFailures", "exportRepeatedFailures", "detectQualificationFailures", "exportQualificationFailures"], "sourceRoot": "src/view/exam/progress", "sources": ["progress-detection.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 统一检测控制 -->\n    <Card style=\"margin-bottom: 20px\">\n      <p slot=\"title\">\n        <Icon type=\"ios-analytics\" />\n        进度检测控制\n      </p>\n      <Row>\n        <Col span=\"6\">\n          <Form-item label=\"提交次数阈值\">\n            <Input-number v-model=\"slowDetection.submissionThreshold\" :min=\"1\" :max=\"100\" style=\"width: 100%\" />\n          </Form-item>\n        </Col>\n        <Col span=\"6\" offset=\"1\">\n          <Form-item label=\"统计天数范围\">\n            <Input-number v-model=\"slowDetection.daysRange\" :min=\"1\" :max=\"30\" style=\"width: 100%\" />\n          </Form-item>\n        </Col>\n        <Col span=\"5\" offset=\"1\">\n          <Form-item label=\"失败次数阈值\">\n            <Input-number v-model=\"repeatedFailures.failureThreshold\" :min=\"1\" :max=\"20\" style=\"width: 100%\" />\n          </Form-item>\n        </Col>\n        <Col span=\"5\" offset=\"1\">\n          <Form-item label=\"失去资格次数阈值\">\n            <Input-number v-model=\"qualificationFailures.failureThreshold\" :min=\"1\" :max=\"20\" style=\"width: 100%\" />\n          </Form-item>\n        </Col>\n      </Row>\n      <Row style=\"margin-top: 10px\">\n        <Col span=\"24\" style=\"text-align: center\">\n          <Button type=\"primary\" @click=\"runAllDetections\" :loading=\"isDetecting\">开始检测</Button>\n        </Col>\n      </Row>\n    </Card>\n\n    <!-- 进度慢检测 -->\n    <Card style=\"margin-bottom: 20px\">\n      <p slot=\"title\">\n        <Icon type=\"ios-speedometer\" />\n        进度慢检测\n        <Button type=\"primary\" size=\"small\" style=\"margin-left: 10px\" @click=\"exportSlowProgress\">导出</Button>\n      </p>\n      <Table :data=\"slowDetection.tableData\" :columns=\"slowDetection.columns\" :loading=\"slowDetection.loading\" border stripe />\n    </Card>\n\n    <!-- 多次挂在同一个P检测 -->\n    <Card style=\"margin-bottom: 20px\">\n      <p slot=\"title\">\n        <Icon type=\"ios-close-circle\" />\n        多次挂在同一个P检测\n        <Button type=\"primary\" size=\"small\" style=\"margin-left: 10px\" @click=\"exportRepeatedFailures\">导出</Button>\n      </p>\n      <Table :data=\"repeatedFailures.tableData\" :columns=\"repeatedFailures.columns\" :loading=\"repeatedFailures.loading\" border stripe />\n    </Card>\n\n    <!-- 多次没有课上资格检测 -->\n    <Card>\n      <p slot=\"title\">\n        <Icon type=\"ios-warning\" />\n        多次没有课上资格检测\n        <Button type=\"primary\" size=\"small\" style=\"margin-left: 10px\" @click=\"exportQualificationFailures\">导出</Button>\n      </p>\n      <Table :data=\"qualificationFailures.tableData\" :columns=\"qualificationFailures.columns\" :loading=\"qualificationFailures.loading\" border stripe />\n    </Card>\n  </div>\n</template>\n\n<script>\nimport { userProfileReq } from '@/api/user'\nimport { getErrModalOptions } from '@/libs/util'\n\nexport default {\n  name: 'ProgressDetection',\n  data() {\n    return {\n      curCourse: null,\n      // 进度慢检测\n      slowDetection: {\n        submissionThreshold: 5,\n        daysRange: 7,\n        loading: false,\n        tableData: [\n          {\n            id: 1,\n            student_name: '张三',\n            student_id: '20210001',\n            department: '计算机学院',\n            submission_count: 2,\n            current_project: 'P1',\n            last_submission_date: '2024-01-15'\n          },\n          {\n            id: 2,\n            student_name: '李四',\n            student_id: '20210002',\n            department: '软件学院',\n            submission_count: 1,\n            current_project: 'P2',\n            last_submission_date: '2024-01-10'\n          }\n        ],\n        columns: [\n          { title: 'ID', key: 'id', width: 80 },\n          { title: '姓名', key: 'student_name', width: 120 },\n          { title: '学号', key: 'student_id', width: 120 },\n          { title: '学院', key: 'department', width: 150 },\n          { title: '提交次数', key: 'submission_count', width: 100 },\n          { title: '当前项目', key: 'current_project', width: 120 },\n          { title: '最后提交日期', key: 'last_submission_date', width: 150 }\n        ]\n      },\n      // 多次挂在同一个P检测\n      repeatedFailures: {\n        failureThreshold: 3,\n        loading: false,\n        tableData: [\n          {\n            id: 1,\n            student_name: '王五',\n            student_id: '20210003',\n            department: '计算机学院',\n            current_project: 'P3',\n            failure_count: 4,\n            last_failure_date: '2024-01-20'\n          },\n          {\n            id: 2,\n            student_name: '赵六',\n            student_id: '20210004',\n            department: '软件学院',\n            current_project: 'P1',\n            failure_count: 5,\n            last_failure_date: '2024-01-18'\n          }\n        ],\n        columns: [\n          { title: 'ID', key: 'id', width: 80 },\n          { title: '姓名', key: 'student_name', width: 120 },\n          { title: '学号', key: 'student_id', width: 120 },\n          { title: '学院', key: 'department', width: 150 },\n          { title: '当前项目', key: 'current_project', width: 120 },\n          { title: '失败次数', key: 'failure_count', width: 100 },\n          { title: '最后失败日期', key: 'last_failure_date', width: 150 }\n        ]\n      },\n      // 多次没有课上资格检测\n      qualificationFailures: {\n        failureThreshold: 2,\n        loading: false,\n        tableData: [\n          {\n            id: 1,\n            student_name: '孙七',\n            student_id: '20210005',\n            department: '计算机学院',\n            current_project: 'P2',\n            missed_exams: 3,\n            current_status: '课下未通过',\n            last_exam_date: '2024-01-22'\n          },\n          {\n            id: 2,\n            student_name: '周八',\n            student_id: '20210006',\n            department: '软件学院',\n            current_project: 'P1',\n            missed_exams: 2,\n            current_status: '课下未通过',\n            last_exam_date: '2024-01-19'\n          }\n        ],\n        columns: [\n          { title: 'ID', key: 'id', width: 80 },\n          { title: '姓名', key: 'student_name', width: 120 },\n          { title: '学号', key: 'student_id', width: 120 },\n          { title: '学院', key: 'department', width: 150 },\n          { title: '当前项目', key: 'current_project', width: 120 },\n          { title: '错过考试次数', key: 'missed_exams', width: 120 },\n          { title: '当前状态', key: 'current_status', width: 120 },\n          { title: '最后考试日期', key: 'last_exam_date', width: 150 }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.loadCurCourse()\n  },\n  methods: {\n    loadCurCourse() {\n      userProfileReq('get')\n        .then((res) => {\n          if (res.data.course === null) {\n            this.$Modal.info({\n              title: '请在课程信息/课程总览页面选择当前课程'\n            })\n          } else {\n            this.curCourse = res.data.course.id\n          }\n        })\n        .catch((error) => {\n          this.$Modal.error(getErrModalOptions(error))\n        })\n    },\n    // 进度慢检测相关方法\n    detectSlowProgress() {\n      if (!this.curCourse) {\n        this.$Modal.warning({ title: '请先选择当前课程' })\n        return\n      }\n      this.slowDetection.loading = true\n      // 模拟API调用\n      setTimeout(() => {\n        this.slowDetection.loading = false\n        this.$Notice.success({\n          title: '检测完成',\n          desc: `发现 ${this.slowDetection.tableData.length} 名进度较慢的学生`\n        })\n      }, 1000)\n    },\n    exportSlowProgress() {\n      this.$Notice.success({ title: '导出成功', desc: '进度慢检测结果已导出到CSV文件' })\n    },\n    // 多次挂在同一个P检测相关方法\n    detectRepeatedFailures() {\n      if (!this.curCourse) {\n        this.$Modal.warning({ title: '请先选择当前课程' })\n        return\n      }\n      this.repeatedFailures.loading = true\n      setTimeout(() => {\n        this.repeatedFailures.loading = false\n        this.$Notice.success({\n          title: '检测完成',\n          desc: `发现 ${this.repeatedFailures.tableData.length} 名多次失败的学生`\n        })\n      }, 1000)\n    },\n    exportRepeatedFailures() {\n      this.$Notice.success({ title: '导出成功', desc: '多次失败检测结果已导出到CSV文件' })\n    },\n    // 多次没有课上资格检测相关方法\n    detectQualificationFailures() {\n      if (!this.curCourse) {\n        this.$Modal.warning({ title: '请先选择当前课程' })\n        return\n      }\n      this.qualificationFailures.loading = true\n      setTimeout(() => {\n        this.qualificationFailures.loading = false\n        this.$Notice.success({\n          title: '检测完成',\n          desc: `发现 ${this.qualificationFailures.tableData.length} 名多次失去资格的学生`\n        })\n      }, 1000)\n    },\n    exportQualificationFailures() {\n      this.$Notice.success({ title: '导出成功', desc: '资格失败检测结果已导出到CSV文件' })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ivu-card {\n  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);\n}\n\n.ivu-card-head p {\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.ivu-form-item {\n  margin-bottom: 0;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}