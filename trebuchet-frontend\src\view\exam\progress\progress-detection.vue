<template>
  <div>
    <!-- 进度慢检测 -->
    <Card style="margin-bottom: 20px">
      <p slot="title">
        <Icon type="ios-speedometer" />
        进度慢检测
      </p>
      <Row>
        <Col span="6">
          <Form-item label="提交次数阈值">
            <Input-number v-model="slowDetection.submissionThreshold" :min="1" :max="100" style="width: 100%" />
          </Form-item>
        </Col>
        <Col span="6" offset="1">
          <Form-item label="统计天数范围">
            <Input-number v-model="slowDetection.daysRange" :min="1" :max="30" style="width: 100%" />
          </Form-item>
        </Col>
        <Col span="4" offset="1">
          <Button type="primary" @click="detectSlowProgress">检测</Button>
          <Button type="success" style="margin-left: 10px" @click="exportSlowProgress">导出</Button>
        </Col>
      </Row>
      <Table
        :data="slowDetection.tableData"
        :columns="slowDetection.columns"
        :loading="slowDetection.loading"
        style="margin-top: 20px"
      />
    </Card>

    <!-- 多次挂在同一个P检测 -->
    <Card style="margin-bottom: 20px">
      <p slot="title">
        <Icon type="ios-close-circle" />
        多次挂在同一个P检测
      </p>
      <Row>
        <Col span="6">
          <Form-item label="失败次数阈值">
            <Input-number v-model="repeatedFailures.failureThreshold" :min="1" :max="20" style="width: 100%" />
          </Form-item>
        </Col>
        <Col span="4" offset="1">
          <Button type="primary" @click="detectRepeatedFailures">检测</Button>
          <Button type="success" style="margin-left: 10px" @click="exportRepeatedFailures">导出</Button>
        </Col>
      </Row>
      <Table
        :data="repeatedFailures.tableData"
        :columns="repeatedFailures.columns"
        :loading="repeatedFailures.loading"
        style="margin-top: 20px"
      />
    </Card>

    <!-- 多次没有课上资格检测 -->
    <Card>
      <p slot="title">
        <Icon type="ios-warning" />
        多次没有课上资格检测
      </p>
      <Row>
        <Col span="6">
          <Form-item label="失败次数阈值">
            <Input-number v-model="qualificationFailures.failureThreshold" :min="1" :max="20" style="width: 100%" />
          </Form-item>
        </Col>
        <Col span="4" offset="1">
          <Button type="primary" @click="detectQualificationFailures">检测</Button>
          <Button type="success" style="margin-left: 10px" @click="exportQualificationFailures">导出</Button>
        </Col>
      </Row>
      <Table
        :data="qualificationFailures.tableData"
        :columns="qualificationFailures.columns"
        :loading="qualificationFailures.loading"
        style="margin-top: 20px"
      />
    </Card>
  </div>
</template>

<script>
import { userProfileReq } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'ProgressDetection',
  data() {
    return {
      curCourse: null,
      // 进度慢检测
      slowDetection: {
        submissionThreshold: 5,
        daysRange: 7,
        loading: false,
        tableData: [
          {
            id: 1,
            student_name: '张三',
            student_id: '20210001',
            department: '计算机学院',
            submission_count: 2,
            current_project: 'P1',
            last_submission_date: '2024-01-15'
          },
          {
            id: 2,
            student_name: '李四',
            student_id: '20210002',
            department: '软件学院',
            submission_count: 1,
            current_project: 'P2',
            last_submission_date: '2024-01-10'
          }
        ],
        columns: [
          { title: 'ID', key: 'id', width: 80 },
          { title: '姓名', key: 'student_name', width: 120 },
          { title: '学号', key: 'student_id', width: 120 },
          { title: '学院', key: 'department', width: 150 },
          { title: '提交次数', key: 'submission_count', width: 100 },
          { title: '当前项目', key: 'current_project', width: 120 },
          { title: '最后提交日期', key: 'last_submission_date', width: 150 }
        ]
      },
      // 多次挂在同一个P检测
      repeatedFailures: {
        failureThreshold: 3,
        loading: false,
        tableData: [
          {
            id: 1,
            student_name: '王五',
            student_id: '20210003',
            department: '计算机学院',
            current_project: 'P3',
            failure_count: 4,
            last_failure_date: '2024-01-20'
          },
          {
            id: 2,
            student_name: '赵六',
            student_id: '20210004',
            department: '软件学院',
            current_project: 'P1',
            failure_count: 5,
            last_failure_date: '2024-01-18'
          }
        ],
        columns: [
          { title: 'ID', key: 'id', width: 80 },
          { title: '姓名', key: 'student_name', width: 120 },
          { title: '学号', key: 'student_id', width: 120 },
          { title: '学院', key: 'department', width: 150 },
          { title: '当前项目', key: 'current_project', width: 120 },
          { title: '失败次数', key: 'failure_count', width: 100 },
          { title: '最后失败日期', key: 'last_failure_date', width: 150 }
        ]
      },
      // 多次没有课上资格检测
      qualificationFailures: {
        failureThreshold: 2,
        loading: false,
        tableData: [
          {
            id: 1,
            student_name: '孙七',
            student_id: '20210005',
            department: '计算机学院',
            current_project: 'P2',
            missed_exams: 3,
            current_status: '课下未通过',
            last_exam_date: '2024-01-22'
          },
          {
            id: 2,
            student_name: '周八',
            student_id: '20210006',
            department: '软件学院',
            current_project: 'P1',
            missed_exams: 2,
            current_status: '课下未通过',
            last_exam_date: '2024-01-19'
          }
        ],
        columns: [
          { title: 'ID', key: 'id', width: 80 },
          { title: '姓名', key: 'student_name', width: 120 },
          { title: '学号', key: 'student_id', width: 120 },
          { title: '学院', key: 'department', width: 150 },
          { title: '当前项目', key: 'current_project', width: 120 },
          { title: '错过考试次数', key: 'missed_exams', width: 120 },
          { title: '当前状态', key: 'current_status', width: 120 },
          { title: '最后考试日期', key: 'last_exam_date', width: 150 }
        ]
      }
    }
  },
  mounted() {
    this.loadCurCourse()
  },
  methods: {
    loadCurCourse() {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course === null) {
            this.$Modal.info({
              title: '请在课程信息/课程总览页面选择当前课程'
            })
          } else {
            this.curCourse = res.data.course.id
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    // 进度慢检测相关方法
    detectSlowProgress() {
      if (!this.curCourse) {
        this.$Modal.warning({ title: '请先选择当前课程' })
        return
      }
      this.slowDetection.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.slowDetection.loading = false
        this.$Notice.success({
          title: '检测完成',
          desc: `发现 ${this.slowDetection.tableData.length} 名进度较慢的学生`
        })
      }, 1000)
    },
    exportSlowProgress() {
      this.$Notice.success({ title: '导出成功', desc: '进度慢检测结果已导出到CSV文件' })
    },
    // 多次挂在同一个P检测相关方法
    detectRepeatedFailures() {
      if (!this.curCourse) {
        this.$Modal.warning({ title: '请先选择当前课程' })
        return
      }
      this.repeatedFailures.loading = true
      setTimeout(() => {
        this.repeatedFailures.loading = false
        this.$Notice.success({
          title: '检测完成',
          desc: `发现 ${this.repeatedFailures.tableData.length} 名多次失败的学生`
        })
      }, 1000)
    },
    exportRepeatedFailures() {
      this.$Notice.success({ title: '导出成功', desc: '多次失败检测结果已导出到CSV文件' })
    },
    // 多次没有课上资格检测相关方法
    detectQualificationFailures() {
      if (!this.curCourse) {
        this.$Modal.warning({ title: '请先选择当前课程' })
        return
      }
      this.qualificationFailures.loading = true
      setTimeout(() => {
        this.qualificationFailures.loading = false
        this.$Notice.success({
          title: '检测完成',
          desc: `发现 ${this.qualificationFailures.tableData.length} 名多次失去资格的学生`
        })
      }, 1000)
    },
    exportQualificationFailures() {
      this.$Notice.success({ title: '导出成功', desc: '资格失败检测结果已导出到CSV文件' })
    }
  }
}
</script>

<style scoped>
.ivu-card {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}

.ivu-card-head p {
  font-size: 16px;
  font-weight: 500;
}

.ivu-form-item {
  margin-bottom: 0;
}
</style>
